import os
import json
import uuid
from typing import Dict, Any, List, Optional, <PERSON><PERSON>
from difflib import SequenceMatcher
import re

class ParentChildLinker:
    def __init__(self, similarity_threshold: float = 0.8):
        self.similarity_threshold = similarity_threshold
        self.dummy_dso_id = "UNMATCHED_DSO"
        
    def process_extracted_data(self, extracted_results: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Second Pass: Link DSO branches to their parent headquarters
        
        Args:
            extracted_results: List of results from first pass extraction
            
        Returns:
            Tuple of (dsos_list, dental_practices_list)
        """
        dsos = []
        branches = []
        independent_practices = []
        
        # Separate data by type
        for result in extracted_results:
            if result['type'] == 'DSO_HQ':
                # DSO headquarters with branches
                dso_data = result['data']
                if isinstance(dso_data, list) and len(dso_data) > 0:
                    # First item is DSO HQ, rest are branches
                    dsos.append(dso_data[0])
                    branches.extend(dso_data[1:])
                else:
                    print(f"⚠️  Unexpected DSO data format in {result['file_path']}")
                    
            elif result['type'] == 'DSO_BRANCH':
                branches.append(result['data'])
                
            elif result['type'] == 'DDS':
                independent_practices.append(result['data'])
        
        # Create DSO lookup table for matching
        dso_lookup = self._create_dso_lookup(dsos)
        
        # Match branches to DSOs
        matched_branches, unmatched_branches = self._match_branches_to_dsos(branches, dso_lookup)
        
        # Combine all dental practices
        all_practices = independent_practices + matched_branches + unmatched_branches
        
        print(f"📊 Processing Summary:")
        print(f"   DSO Headquarters: {len(dsos)}")
        print(f"   Matched Branches: {len(matched_branches)}")
        print(f"   Unmatched Branches: {len(unmatched_branches)}")
        print(f"   Independent Practices: {len(independent_practices)}")
        print(f"   Total Dental Practices: {len(all_practices)}")
        
        return dsos, all_practices
    
    def _create_dso_lookup(self, dsos: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """Create lookup table for DSO matching"""
        lookup = {}
        for dso in dsos:
            if dso.get('name'):
                # Normalize name for matching
                normalized_name = self._normalize_name(dso['name'])
                lookup[normalized_name] = dso
        return lookup
    
    def _normalize_name(self, name: str) -> str:
        """Normalize business name for matching"""
        if not name:
            return ""
        
        # Convert to lowercase and remove common business suffixes/prefixes
        normalized = name.lower().strip()
        
        # Remove common business entity types
        entity_patterns = [
            r'\b(llc|inc|corp|corporation|ltd|limited|co|company|pllc|pc)\b',
            r'\b(dental|dentistry|dentist|group|associates|practice|clinic)\b',
            r'\b(the|a|an)\b'
        ]
        
        for pattern in entity_patterns:
            normalized = re.sub(pattern, '', normalized)
        
        # Remove extra whitespace and punctuation
        normalized = re.sub(r'[^\w\s]', '', normalized)
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        
        return normalized
    
    def _calculate_similarity(self, name1: str, name2: str) -> float:
        """Calculate similarity between two business names"""
        norm1 = self._normalize_name(name1)
        norm2 = self._normalize_name(name2)
        
        if not norm1 or not norm2:
            return 0.0
        
        return SequenceMatcher(None, norm1, norm2).ratio()
    
    def _match_branches_to_dsos(self, branches: List[Dict[str, Any]], dso_lookup: Dict[str, Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Match branches to DSO headquarters using fuzzy string matching"""
        matched_branches = []
        unmatched_branches = []
        
        for branch in branches:
            branch_name = branch.get('name', '')
            best_match = None
            best_score = 0.0
            
            # Try exact normalized match first
            normalized_branch = self._normalize_name(branch_name)
            if normalized_branch in dso_lookup:
                best_match = dso_lookup[normalized_branch]
                best_score = 1.0
            else:
                # Fuzzy matching
                for dso_name, dso_data in dso_lookup.items():
                    similarity = self._calculate_similarity(branch_name, dso_data['name'])
                    if similarity > best_score:
                        best_score = similarity
                        best_match = dso_data
            
            # Assign DSO ID based on match confidence
            if best_match and best_score >= self.similarity_threshold:
                branch['dso_id'] = best_match['id']
                matched_branches.append(branch)
                print(f"✅ Matched '{branch_name}' to '{best_match['name']}' (score: {best_score:.2f})")
            else:
                branch['dso_id'] = self.dummy_dso_id
                unmatched_branches.append(branch)
                if best_match:
                    print(f"❌ Low confidence match for '{branch_name}' to '{best_match['name']}' (score: {best_score:.2f})")
                else:
                    print(f"❌ No match found for '{branch_name}'")
        
        return matched_branches, unmatched_branches
    
    def save_results(self, dsos: List[Dict[str, Any]], practices: List[Dict[str, Any]], 
                    dsos_output: str = "dsos.json", practices_output: str = "dentalpractices.json"):
        """Save results to separate JSON files"""
        
        # Save DSOs
        with open(dsos_output, 'w', encoding='utf-8') as f:
            json.dump(dsos, f, indent=4, ensure_ascii=False)
        print(f"💾 Saved {len(dsos)} DSO headquarters to {dsos_output}")
        
        # Save dental practices
        with open(practices_output, 'w', encoding='utf-8') as f:
            json.dump(practices, f, indent=4, ensure_ascii=False)
        print(f"💾 Saved {len(practices)} dental practices to {practices_output}")
        
        # Generate summary report
        self._generate_summary_report(dsos, practices)
    
    def _generate_summary_report(self, dsos: List[Dict[str, Any]], practices: List[Dict[str, Any]]):
        """Generate a summary report of the processing results"""
        
        matched_count = sum(1 for p in practices if p.get('dso_id') and p['dso_id'] != self.dummy_dso_id and p['dso_id'] is not None)
        unmatched_count = sum(1 for p in practices if p.get('dso_id') == self.dummy_dso_id)
        independent_count = sum(1 for p in practices if p.get('dso_id') is None)
        
        report = f"""
📋 PROCESSING SUMMARY REPORT
{'='*50}

DSO HEADQUARTERS: {len(dsos)}
DENTAL PRACTICES: {len(practices)}
  ├── Independent Practices: {independent_count}
  ├── Matched to DSO: {matched_count}
  └── Unmatched Branches: {unmatched_count}

MATCH RATE: {(matched_count / (matched_count + unmatched_count) * 100):.1f}% (of branches)

FILES GENERATED:
  ├── dsos.json ({len(dsos)} records)
  └── dentalpractices.json ({len(practices)} records)

NEXT STEPS:
  • Review unmatched branches in dentalpractices.json (dso_id = "{self.dummy_dso_id}")
  • Manual review recommended for low-confidence matches
  • Consider adjusting similarity threshold (current: {self.similarity_threshold})
"""
        
        print(report)
        
        # Save report to file
        with open("processing_report.txt", "w", encoding="utf-8") as f:
            f.write(report)
        print("📄 Detailed report saved to processing_report.txt")


def process_directory_with_linking(directory_path: str, similarity_threshold: float = 0.8):
    """
    Complete two-pass processing of a directory
    """
    from unifiedExtractor import UnifiedBBBExtractor
    
    if not os.path.exists(directory_path):
        raise ValueError(f"Directory not found: {directory_path}")
    
    # Get all HTML files
    html_files = []
    for root, dirs, files in os.walk(directory_path):
        for file in files:
            if file.lower().endswith('.html'):
                html_files.append(os.path.join(root, file))
    
    if not html_files:
        raise ValueError(f"No HTML files found in directory: {directory_path}")
    
    print(f"🔍 Found {len(html_files)} HTML files to process")
    
    # First Pass: Classification and extraction
    extractor = UnifiedBBBExtractor()
    extracted_results = []
    
    for html_file in html_files:
        try:
            result = extractor.process_html_file(html_file)
            extracted_results.append(result)
            print(f"✅ {result['type']}: {os.path.basename(html_file)}")
        except Exception as e:
            print(f"❌ Error processing {html_file}: {e}")
            continue
    
    # Second Pass: Parent-child linking
    linker = ParentChildLinker(similarity_threshold)
    dsos, practices = linker.process_extracted_data(extracted_results)
    
    # Save results
    linker.save_results(dsos, practices)
    
    return dsos, practices


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python parentChildLinker.py <directory_path> [similarity_threshold]")
        sys.exit(1)
    
    directory = sys.argv[1]
    threshold = float(sys.argv[2]) if len(sys.argv) > 2 else 0.8
    
    try:
        process_directory_with_linking(directory, threshold)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
