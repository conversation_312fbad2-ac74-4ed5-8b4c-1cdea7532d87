import re
import uuid
from datetime import datetime
from typing import Dict, Any
from bs4 import BeautifulSoup
from ddsProfileExtractor import BBBProfileExtractor

class UnifiedBBBExtractor:
    def __init__(self):
        self.dds_extractor = BBBProfileExtractor()

    def process_html_file(self, file_path: str) -> Dict[str, Any]:
        with open(file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()

        soup = BeautifulSoup(html_content, 'html.parser')
        if not soup.find(id='businessName'):
            raise ValueError("Invalid HTML file")

        classification = self._classify_type(html_content)

        if classification == 'DSO_HQ':
            return {'type': 'DSO_HQ', 'data': self._extract_dso(soup), 'file_path': file_path}

        profile_data = self.dds_extractor.extract_profile(html_content)
        if classification == 'DSO_BRANCH':
            profile_data['dso_id'] = 'PENDING_MATCH'

        return {'type': classification, 'data': profile_data, 'file_path': file_path}

    def _classify_type(self, html_content: str) -> str:
        if 'Find Local Branches' in html_content:
            return 'DSO_HQ'
        if 'View Headquarters' in html_content:
            return 'DSO_BRANCH'
        return 'DDS'

    def _extract_dso(self, soup: BeautifulSoup) -> Dict[str, Any]:
        data = {
            "id": str(uuid.uuid4()),
            "name": None,
            "website": None,
            "phone": None,
            "email": None,
            "hq_street": None,
            "hq_city": None,
            "hq_state": None,
            "hq_zip": None,
            "notes": None,
            "created_at": datetime.now().isoformat()
        }

        business_name = soup.find(id='businessName')
        if business_name:
            data['name'] = business_name.get_text(strip=True)

        data['website'] = self.dds_extractor._extract_website(soup)

        address_data = {}
        self.dds_extractor._extract_address(soup, address_data)
        if address_data:
            data['hq_street'] = address_data.get('street')
            data['hq_city'] = address_data.get('city')
            data['hq_state'] = address_data.get('state')
            data['hq_zip'] = address_data.get('zip')

        phone_link = soup.find('a', href=re.compile(r'^tel:'))
        if phone_link:
            data['phone'] = self.dds_extractor._clean_phone(phone_link.get_text(strip=True))

        return data