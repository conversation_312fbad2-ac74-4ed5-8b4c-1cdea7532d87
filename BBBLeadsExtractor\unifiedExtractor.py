import re
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from bs4 import BeautifulSoup
from ddsProfileExtractor import BBBProfileExtractor

class UnifiedBBBExtractor:
    def __init__(self):
        self.dds_extractor = BBBProfileExtractor()
        self.dso_indicators = [
            'Find local Branches',
            'Find Local Branches'
        ]
        self.branch_indicators = [
            'View Headquarters',
            'view headquarters'
        ]

    def process_html_file(self, file_path: str, output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        First Pass: Classification only - identify HTML type and extract basic data
        Returns: {'type': 'DSO_HQ'|'DSO_BRANCH'|'DDS', 'data': extracted_data, 'file_path': file_path}
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()

        soup = BeautifulSoup(html_content, 'html.parser')

        business_name = soup.find(id='businessName')
        if not business_name:
            raise ValueError("No business profile content found - file may be incomplete or corrupted")

        # Classification logic
        classification = self._classify_business_type(soup, html_content)

        if classification == 'DSO_HQ':
            # Extract DSO headquarters data (simplified structure)
            dso_data = self._create_dso_headquarters(soup)
            return {
                'type': 'DSO_HQ',
                'data': dso_data,
                'file_path': file_path
            }

        # For both DSO_BRANCH and DDS, extract using DDS extractor
        profile_data = self.dds_extractor.extract_profile(html_content)
        if not profile_data.get('name'):
            raise ValueError("Failed to extract business name from profile")

        # Mark branches for second pass linking
        if classification == 'DSO_BRANCH':
            profile_data['dso_id'] = 'PENDING_MATCH'

        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(profile_data, f, indent=4, ensure_ascii=False)

        return {
            'type': classification,
            'data': profile_data,
            'file_path': file_path
        }

    def _classify_business_type(self, soup: BeautifulSoup, html_content: str) -> str:
        """
        Classify business type based on HTML content
        Returns: 'DSO_HQ', 'DSO_BRANCH', or 'DDS'
        """
        # Check for DSO headquarters indicators first
        for indicator in self.dso_indicators:
            if indicator in html_content:
                return 'DSO_HQ'

        # Check for DSO branch indicators
        for indicator in self.branch_indicators:
            if indicator in html_content:
                return 'DSO_BRANCH'

        # Default to independent practice
        return 'DDS'

    def _create_dso_headquarters(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Create DSO headquarters record (simplified structure)"""
        dso_id = str(uuid.uuid4())

        data = {
            "id": dso_id,
            "name": None,
            "website": None,
            "phone": None,
            "email": None,
            "hq_city": None,
            "hq_state": None,
            "hq_zip": None,
            "notes": None,
            "created_at": datetime.now().isoformat()
        }

        # Extract basic DSO info
        business_name_elem = soup.find(id='businessName')
        if business_name_elem:
            data['name'] = business_name_elem.get_text(strip=True)

        data['website'] = self.dds_extractor._extract_website(soup)

        # Extract headquarters address from first business card
        business_cards = soup.find_all(class_='find-business-card')
        if business_cards:
            hq_card = business_cards[0]
            hq_address = hq_card.find('p', class_='font-bold')
            if hq_address:
                address_text = hq_address.get_text(strip=True)
                parts = address_text.split('  ')
                if len(parts) >= 2:
                    location_data = {}
                    self.dds_extractor._parse_location(parts[1].strip(), location_data)
                    data['hq_city'] = location_data.get('city')
                    data['hq_state'] = location_data.get('state')
                    data['hq_zip'] = location_data.get('zip')

        # Extract phone
        phone_link = soup.find('a', href=re.compile(r'^tel:'))
        if phone_link:
            data['phone'] = self.dds_extractor._clean_phone(phone_link.get_text(strip=True))

        return data

