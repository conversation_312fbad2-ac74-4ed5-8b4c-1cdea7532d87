import os
import re
import json
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from bs4 import BeautifulSoup
from ddsProfileExtractor import BBBProfileExtractor

class UnifiedBBBExtractor:
    def __init__(self):
        self.dds_extractor = BBBProfileExtractor()
        self.dso_indicators = [
            'Find local Branches',
            'Find Local Branches', 
            'find-business-card',
            'has 2 locations',
            'has 3 locations',
            'has 4 locations',
            'has 5 locations'
        ]

    def process_html_file(self, file_path: str, output_file: Optional[str] = None) -> List[Dict[str, Any]]:
        with open(file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()

        soup = BeautifulSoup(html_content, 'html.parser')

        business_name = soup.find(id='businessName')
        if not business_name:
            raise ValueError("No business profile content found - file may be incomplete or corrupted")

        if self._is_dso(soup, html_content):
            try:
                return self._process_dso(soup, output_file)
            except Exception as dso_error:
                print(f"⚠️  DSO processing failed: {dso_error}")
                print(f"Failed to process file: {os.path.basename(file_path)}")
                print(f"📄 Falling back to DDS extraction")

        single_profile = self.dds_extractor.extract_profile(html_content)
        if not single_profile.get('name'):
            raise ValueError("Failed to extract business name from single practice profile")
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(single_profile, f, indent=4, ensure_ascii=False)
        return [single_profile]

    def _is_dso(self, soup: BeautifulSoup, html_content: str) -> bool:
        business_name = soup.find(id='businessName')
        if not business_name:
            return False

        business_cards = soup.find_all(class_='find-business-card')
        valid_cards = []
        for card in business_cards:
            name_elem = card.find('h3', class_='bds-h4')
            address_elem = card.find('p', class_='font-bold')
            if name_elem and address_elem:
                valid_cards.append(card)

        if len(valid_cards) > 1:
            return True

        for indicator in ['Find local Branches', 'Find Local Branches']:
            if indicator in html_content and len(valid_cards) > 0:
                return True

        return False

    def _process_dso(self, soup: BeautifulSoup, output_file: Optional[str] = None) -> List[Dict[str, Any]]:
        results = []

        parent_dso = self._create_parent_dso(soup)
        if not parent_dso.get('name'):
            raise ValueError("DSO missing business name")
            

        results.append(parent_dso)

        branches = self._extract_branches(soup, parent_dso['id'])
        if not branches:
            raise ValueError("DSO detected but no branches found")

        results.extend(branches)

        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=4, ensure_ascii=False)

        return results

    def _create_parent_dso(self, soup: BeautifulSoup) -> Dict[str, Any]:
        dso_id = str(uuid.uuid4())
        business_cards = soup.find_all(class_='find-business-card')

        data = {
            "id": dso_id,
            "name": None,
            "website": None,
            "phone": None,
            "email": None,
            "hq_city": None,
            "hq_state": None,
            "hq_zip": None,
            "notes": None,
            "created_at": datetime.now().isoformat()
        }

        business_name_elem = soup.find(id='businessName')
        if business_name_elem:
            data['name'] = business_name_elem.get_text(strip=True)

        data['website'] = self.dds_extractor._extract_website(soup)

        if business_cards:
            hq_card = business_cards[0]
            hq_address = hq_card.find('p', class_='font-bold')
            if hq_address:
                address_text = hq_address.get_text(strip=True)
                parts = address_text.split('  ')
                if len(parts) >= 2:
                    location_data = {}
                    self.dds_extractor._parse_location(parts[1].strip(), location_data)
                    data['hq_city'] = location_data.get('city')
                    data['hq_state'] = location_data.get('state')
                    data['hq_zip'] = location_data.get('zip')

        phone_link = soup.find('a', href=re.compile(r'^tel:'))
        if phone_link:
            data['phone'] = self.dds_extractor._clean_phone(phone_link.get_text(strip=True))

        return data

    def _extract_branches(self, soup: BeautifulSoup, parent_dso_id: str) -> List[Dict[str, Any]]:
        branches = []
        business_cards = soup.find_all(class_='find-business-card')

        if not business_cards:
            raise ValueError("No business cards found for DSO branches")

        for i, card in enumerate(business_cards):
            if i == 0:
                continue

            branch_data = self._create_branch_profile(parent_dso_id, card, soup)
            if not branch_data.get('name') or not branch_data.get('street'):
                raise ValueError(f"Branch {i} missing critical data (name/address)")


            branches.append(branch_data)

        return branches

    def _create_branch_profile(self, parent_dso_id: str, card_element, main_soup: BeautifulSoup) -> Dict[str, Any]:
        data = self.dds_extractor._initialize_data()
        data['dso_id'] = parent_dso_id

        name_link = card_element.find('h3', class_='bds-h4')
        if name_link:
            link_elem = name_link.find('a')
            if link_elem:
                data['name'] = link_elem.get_text(strip=True)

        address_elem = card_element.find('p', class_='font-bold')
        if address_elem:
            address_text = address_elem.get_text(strip=True)
            if '  ' in address_text:
                parts = address_text.split('  ')
                if len(parts) >= 2:
                    data['street'] = parts[0].strip()
                    self.dds_extractor._parse_location(parts[1].strip(), data)
            else:
                parts = address_text.split(',')
                if len(parts) >= 3:
                    data['street'] = parts[0].strip()
                    city_state_zip = ','.join(parts[1:]).strip()
                    self.dds_extractor._parse_location(city_state_zip, data)

        self._inherit_dso_data(data, main_soup)
        self.dds_extractor._clean_data(data)
        return data

    def _inherit_dso_data(self, branch_data: Dict[str, Any], main_soup: BeautifulSoup):
        branch_data['website'] = self.dds_extractor._extract_website(main_soup)
        branch_data['bbb_rating'] = self.dds_extractor._extract_rating(main_soup)

        phone_link = main_soup.find('a', href=re.compile(r'^tel:'))
        if phone_link:
            branch_data['phone'] = self.dds_extractor._clean_phone(phone_link.get_text(strip=True))

        self.dds_extractor._extract_business_details(main_soup, branch_data)
        self.dds_extractor._normalize_dates(branch_data)