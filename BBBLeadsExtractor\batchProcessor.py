#!/usr/bin/env python3
"""
Batch processor for BBB data extraction using two-pass approach

Usage:
    python batchProcessor.py <input_directory> [--threshold 0.8] [--output-dir ./output]

Example:
    python batchProcessor.py ./html_files --threshold 0.85 --output-dir ./results
"""

import os
import sys
import argparse
from pathlib import Path
from parentChildLinker import process_directory_with_linking

def main():
    parser = argparse.ArgumentParser(
        description="Process BBB HTML files using two-pass DSO classification and linking",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python batchProcessor.py ./html_files
  python batchProcessor.py ./data --threshold 0.85
  python batchProcessor.py ./input --output-dir ./results --threshold 0.9

Two-Pass Process:
  1. Classification: Identifies DSO HQ, DSO Branch, or Independent Practice
  2. Linking: Matches branches to headquarters using fuzzy string matching

Output Files:
  - dsos.json: DSO headquarters data
  - dentalpractices.json: All dental practices (independent + branches)
  - processing_report.txt: Summary report with statistics
        """
    )
    
    parser.add_argument(
        'input_directory',
        help='Directory containing HTML files to process'
    )
    
    parser.add_argument(
        '--threshold', '-t',
        type=float,
        default=0.8,
        help='Similarity threshold for branch-to-DSO matching (0.0-1.0, default: 0.8)'
    )
    
    parser.add_argument(
        '--output-dir', '-o',
        default='.',
        help='Output directory for result files (default: current directory)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )
    
    args = parser.parse_args()
    
    # Validate inputs
    if not os.path.exists(args.input_directory):
        print(f"❌ Error: Input directory '{args.input_directory}' does not exist")
        sys.exit(1)
    
    if not os.path.isdir(args.input_directory):
        print(f"❌ Error: '{args.input_directory}' is not a directory")
        sys.exit(1)
    
    if not (0.0 <= args.threshold <= 1.0):
        print(f"❌ Error: Threshold must be between 0.0 and 1.0, got {args.threshold}")
        sys.exit(1)
    
    # Create output directory if it doesn't exist
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Change to output directory for file generation
    original_cwd = os.getcwd()
    os.chdir(output_dir)
    
    try:
        print(f"🚀 Starting two-pass BBB data extraction")
        print(f"📁 Input Directory: {args.input_directory}")
        print(f"📁 Output Directory: {output_dir.absolute()}")
        print(f"🎯 Similarity Threshold: {args.threshold}")
        print(f"{'='*60}")
        
        # Process the directory
        dsos, practices = process_directory_with_linking(
            args.input_directory, 
            args.threshold
        )
        
        print(f"{'='*60}")
        print(f"✅ Processing completed successfully!")
        print(f"📊 Results:")
        print(f"   • DSO Headquarters: {len(dsos)}")
        print(f"   • Dental Practices: {len(practices)}")
        print(f"📁 Output files saved to: {output_dir.absolute()}")
        
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)
    
    finally:
        # Restore original working directory
        os.chdir(original_cwd)

if __name__ == "__main__":
    main()
