import os
import sys
import argparse
from parentChildLinker import process_directory_with_linking

def main():
    parser = argparse.ArgumentParser(description="Process BBB HTML files")
    parser.add_argument('input_directory', help='Directory with HTML files')
    parser.add_argument('--threshold', '-t', type=float, default=0.8, help='Matching threshold (default: 0.8)')
    parser.add_argument('--output-dir', '-o', default='.', help='Output directory (default: current)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')

    args = parser.parse_args()

    if not os.path.exists(args.input_directory):
        print(f"❌ Directory not found: {args.input_directory}")
        sys.exit(1)

    if not (0.0 <= args.threshold <= 1.0):
        print(f"❌ Invalid threshold: {args.threshold}")
        sys.exit(1)

    os.makedirs(args.output_dir, exist_ok=True)
    original_cwd = os.getcwd()
    os.chdir(args.output_dir)

    try:
        print(f"🚀 Processing {args.input_directory} (threshold: {args.threshold})")
        print("="*60)

        dsos, practices = process_directory_with_linking(args.input_directory, args.threshold)

        print("="*60)
        print(f"✅ Complete! DSOs: {len(dsos)}, Practices: {len(practices)}")

    except Exception as e:
        print(f"❌ Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)
    finally:
        os.chdir(original_cwd)

if __name__ == "__main__":
    main()
